package test;

import manager.GameStatus;
import manager.MultiplayerManager;
import manager.GameEngine;

/**
 * Simple test to debug reconnection behavior
 */
public class ReconnectionDebugTest {
    
    public static void main(String[] args) {
        System.out.println("=== Reconnection Debug Test ===");
        
        // Create a mock game engine
        MockGameEngine gameEngine = new MockGameEngine();
        
        // Create a mock multiplayer manager
        MockMultiplayerManager multiplayerManager = new MockMultiplayerManager(gameEngine);
        
        // Test 1: Initial connection (should go to MAP_SELECTION)
        System.out.println("\n--- Test 1: Initial Connection ---");
        gameEngine.setGameStatus(GameStatus.START_SCREEN);
        System.out.println("Initial status: " + gameEngine.getGameStatus());
        
        multiplayerManager.onConnectionEstablished();
        System.out.println("Status after onConnectionEstablished: " + gameEngine.getGameStatus());
        System.out.println("Expected: MAP_SELECTION, Actual: " + gameEngine.getGameStatus());
        System.out.println("Test 1 " + (gameEngine.getGameStatus() == GameStatus.MAP_SELECTION ? "PASSED" : "FAILED"));
        
        // Test 2: Reconnection (should go to WAITING_FOR_SERVER)
        System.out.println("\n--- Test 2: Reconnection ---");
        gameEngine.setGameStatus(GameStatus.RECONNECTING);
        System.out.println("Initial status: " + gameEngine.getGameStatus());
        
        multiplayerManager.onConnectionEstablished();
        System.out.println("Status after onConnectionEstablished: " + gameEngine.getGameStatus());
        System.out.println("Expected: WAITING_FOR_SERVER, Actual: " + gameEngine.getGameStatus());
        System.out.println("Test 2 " + (gameEngine.getGameStatus() == GameStatus.WAITING_FOR_SERVER ? "PASSED" : "FAILED"));
        
        // Test 3: Simulate double call scenario
        System.out.println("\n--- Test 3: Double Call Scenario ---");
        gameEngine.setGameStatus(GameStatus.RECONNECTING);
        System.out.println("Initial status: " + gameEngine.getGameStatus());
        
        // First call
        multiplayerManager.onConnectionEstablished();
        System.out.println("Status after first call: " + gameEngine.getGameStatus());
        
        // Second call (simulating the potential double call issue)
        multiplayerManager.onConnectionEstablished();
        System.out.println("Status after second call: " + gameEngine.getGameStatus());
        System.out.println("Expected: WAITING_FOR_SERVER, Actual: " + gameEngine.getGameStatus());
        System.out.println("Test 3 " + (gameEngine.getGameStatus() == GameStatus.WAITING_FOR_SERVER ? "PASSED" : "FAILED"));
    }
    
    static class MockGameEngine {
        private GameStatus gameStatus = GameStatus.START_SCREEN;
        
        public GameStatus getGameStatus() {
            return gameStatus;
        }
        
        public void setGameStatus(GameStatus status) {
            System.out.println("  GameEngine.setGameStatus: " + this.gameStatus + " -> " + status);
            this.gameStatus = status;
        }
    }
    
    static class MockMultiplayerManager {
        private final MockGameEngine gameEngine;
        private boolean isHost = false;
        
        public MockMultiplayerManager(MockGameEngine gameEngine) {
            this.gameEngine = gameEngine;
        }
        
        public void onConnectionEstablished() {
            String role = isHost ? "[Server]" : "[Client]";
            System.out.println("  " + role + " onConnectionEstablished called");
            
            // Check if this is a reconnection scenario
            GameStatus currentStatus = gameEngine.getGameStatus();
            System.out.println("  Current status: " + currentStatus);
            
            if (currentStatus == GameStatus.RECONNECTING) {
                System.out.println("  " + role + " Reconnection detected - waiting for server state sync");
                gameEngine.setGameStatus(GameStatus.WAITING_FOR_SERVER);
            } else {
                // Initial connection - go to map selection
                System.out.println("  " + role + " Initial connection - switching to MAP_SELECTION");
                gameEngine.setGameStatus(GameStatus.MAP_SELECTION);
            }
        }
    }
}
